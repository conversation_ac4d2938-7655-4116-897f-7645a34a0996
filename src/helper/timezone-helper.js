/**
 * Timezone conversion utilities for handling doctor-patient timezone differences
 */

/**
 * Get user's current timezone
 */
export const getUserTimezone = () => {
  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  } catch (error) {
    console.error("Error detecting timezone:", error);
    return "UTC";
  }
};

/**
 * Convert time from doctor's timezone to patient's timezone
 * @param {string} timeString - Time in HH:mm:ss format
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {string} doctorTimezone - Doctor's timezone (e.g., "America/New_York")
 * @param {string} patientTimezone - Patient's timezone (e.g., "Asia/Karachi")
 * @returns {Object} - { time: string, date: string, isNextDay: boolean }
 */
export const convertTimeToPatientTimezone = (
  timeString,
  date,
  doctorTimezone,
  patientTimezone
) => {
  if (!doctorTimezone || !patientTimezone || doctorTimezone === patientTimezone) {
    return { time: timeString, date, isNextDay: false };
  }

  try {
    // Create a date object in doctor's timezone
    const doctorDateTime = new Date(`${date}T${timeString}`);
    
    // Get the time in doctor's timezone
    const doctorTimeInUTC = new Date(
      doctorDateTime.toLocaleString("en-US", { timeZone: "UTC" })
    );
    
    // Adjust for doctor's timezone offset
    const doctorOffset = getDoctorTimezoneOffset(doctorDateTime, doctorTimezone);
    const adjustedUTC = new Date(doctorTimeInUTC.getTime() - doctorOffset);
    
    // Convert to patient's timezone
    const patientTime = new Date(
      adjustedUTC.toLocaleString("en-US", { timeZone: patientTimezone })
    );
    
    const originalDate = new Date(date);
    const isNextDay = patientTime.getDate() !== originalDate.getDate();
    
    return {
      time: patientTime.toTimeString().slice(0, 8), // HH:mm:ss
      date: patientTime.toISOString().slice(0, 10), // YYYY-MM-DD
      isNextDay,
    };
  } catch (error) {
    console.error("Error converting timezone:", error);
    return { time: timeString, date, isNextDay: false };
  }
};

/**
 * Get timezone offset for a specific date and timezone
 */
const getDoctorTimezoneOffset = (date, timezone) => {
  try {
    const utc = new Date(date.toLocaleString("en-US", { timeZone: "UTC" }));
    const local = new Date(date.toLocaleString("en-US", { timeZone: timezone }));
    return local.getTime() - utc.getTime();
  } catch (error) {
    return 0;
  }
};

/**
 * Format time for display with timezone information
 * @param {string} timeString - Time in HH:mm:ss format
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {string} doctorTimezone - Doctor's timezone
 * @param {string} patientTimezone - Patient's timezone
 * @returns {Object} - Formatted time information
 */
export const formatTimeWithTimezone = (
  timeString,
  date,
  doctorTimezone,
  patientTimezone
) => {
  const converted = convertTimeToPatientTimezone(
    timeString,
    date,
    doctorTimezone,
    patientTimezone
  );
  
  // Format time for display (12-hour format)
  const formatTime = (time) => {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  const originalFormatted = formatTime(timeString);
  const convertedFormatted = formatTime(converted.time);
  
  return {
    original: {
      time: timeString,
      formatted: originalFormatted,
      timezone: doctorTimezone,
    },
    converted: {
      time: converted.time,
      formatted: convertedFormatted,
      timezone: patientTimezone,
      date: converted.date,
      isNextDay: converted.isNextDay,
    },
    displayTime: convertedFormatted,
    displayDate: converted.date,
  };
};

/**
 * Get timezone display name
 * @param {string} timezone - Timezone identifier
 * @returns {string} - Human readable timezone name
 */
export const getTimezoneDisplayName = (timezone) => {
  try {
    const now = new Date();
    const formatter = new Intl.DateTimeFormat('en-US', {
      timeZone: timezone,
      timeZoneName: 'short'
    });
    
    const parts = formatter.formatToParts(now);
    const timeZoneName = parts.find(part => part.type === 'timeZoneName');
    return timeZoneName ? timeZoneName.value : timezone;
  } catch (error) {
    return timezone;
  }
};

/**
 * Check if timezone conversion is needed
 * @param {string} doctorTimezone 
 * @param {string} patientTimezone 
 * @returns {boolean}
 */
export const isTimezoneConversionNeeded = (doctorTimezone, patientTimezone) => {
  return doctorTimezone && patientTimezone && doctorTimezone !== patientTimezone;
};
