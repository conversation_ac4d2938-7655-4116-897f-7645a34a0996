/**
 * Timezone conversion utilities for handling doctor-patient timezone differences
 */

/**
 * Get user's current timezone
 */
export const getUserTimezone = () => {
  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  } catch (error) {
    console.error("Error detecting timezone:", error);
    return "UTC";
  }
};

/**
 * Convert time from doctor's timezone to patient's timezone
 * @param {string} timeString - Time in HH:mm:ss format
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {string} doctorTimezone - Doctor's timezone (e.g., "Asia/Shanghai")
 * @param {string} patientTimezone - Patient's timezone (e.g., "Asia/Karachi")
 * @returns {Object} - { time: string, date: string, isNextDay: boolean }
 */
export const convertTimeToPatientTimezone = (
  timeString,
  date,
  doctorTimezone,
  patientTimezone,
) => {
  if (
    !doctorTimezone ||
    !patientTimezone ||
    doctorTimezone === patientTimezone
  ) {
    return { time: timeString, date, isNextDay: false };
  }

  try {
    // Create a datetime string representing the doctor's local time
    const doctorDateTime = `${date}T${timeString}`;

    // Use a much simpler approach with Intl.DateTimeFormat
    // Create a Date object (this will be in the browser's local timezone)
    const baseDate = new Date(doctorDateTime);

    // Format this date as if it were in the doctor's timezone
    const doctorFormatter = new Intl.DateTimeFormat("en-CA", {
      timeZone: doctorTimezone,
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
    });

    // Format this date as if it were in the patient's timezone
    const patientFormatter = new Intl.DateTimeFormat("en-CA", {
      timeZone: patientTimezone,
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
    });

    // Get the difference between doctor's timezone and UTC
    const utcFormatter = new Intl.DateTimeFormat("en-CA", {
      timeZone: "UTC",
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
    });

    // Calculate what time it would be in UTC if the doctor's local time is our input
    const doctorLocal = doctorFormatter.format(baseDate);
    const utcTime = utcFormatter.format(baseDate);

    // Parse the times to calculate offset
    const doctorMs = new Date(doctorLocal.replace(", ", "T")).getTime();
    const utcMs = new Date(utcTime.replace(", ", "T")).getTime();
    const offsetMs = doctorMs - utcMs;

    // Create the correct UTC time by adjusting for the doctor's timezone offset
    const correctUtcTime = new Date(baseDate.getTime() - offsetMs);

    // Now format this UTC time in the patient's timezone
    const patientLocalTime = patientFormatter.format(correctUtcTime);

    // Parse the result
    const [datePart, timePart] = patientLocalTime.split(", ");

    const originalDate = new Date(date);
    const convertedDate = new Date(datePart);
    const isNextDay = convertedDate.getDate() !== originalDate.getDate();

    return {
      time: timePart,
      date: datePart,
      isNextDay,
    };
  } catch (error) {
    console.error("Error converting timezone:", error);
    return { time: timeString, date, isNextDay: false };
  }
};

/**
 * Get timezone offset in minutes for a specific timezone and date
 * @param {string} timezone - Timezone identifier
 * @param {Date} date - Date object
 * @returns {number} - Offset in minutes from UTC (positive for east of UTC, negative for west)
 */
const getTimezoneOffsetMinutes = (timezone, date) => {
  try {
    // Create two dates: one in UTC and one in the target timezone
    const utcDate = new Date(date.toLocaleString("en-US", { timeZone: "UTC" }));
    const targetDate = new Date(
      date.toLocaleString("en-US", { timeZone: timezone }),
    );

    // Calculate the difference in minutes
    const offsetMs = targetDate.getTime() - utcDate.getTime();
    return Math.round(offsetMs / (1000 * 60));
  } catch (error) {
    console.error("Error getting timezone offset:", error);
    return 0;
  }
};

/**
 * Format time for display with timezone information
 * @param {string} timeString - Time in HH:mm:ss format
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {string} doctorTimezone - Doctor's timezone
 * @param {string} patientTimezone - Patient's timezone
 * @returns {Object} - Formatted time information
 */
export const formatTimeWithTimezone = (
  timeString,
  date,
  doctorTimezone,
  patientTimezone,
) => {
  const converted = convertTimeToPatientTimezone(
    timeString,
    date,
    doctorTimezone,
    patientTimezone,
  );

  // Format time for display (12-hour format)
  const formatTime = (time) => {
    const [hours, minutes] = time.split(":");
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? "PM" : "AM";
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  const originalFormatted = formatTime(timeString);
  const convertedFormatted = formatTime(converted.time);

  return {
    original: {
      time: timeString,
      formatted: originalFormatted,
      timezone: doctorTimezone,
    },
    converted: {
      time: converted.time,
      formatted: convertedFormatted,
      timezone: patientTimezone,
      date: converted.date,
      isNextDay: converted.isNextDay,
    },
    displayTime: convertedFormatted,
    displayDate: converted.date,
  };
};

/**
 * Get timezone display name
 * @param {string} timezone - Timezone identifier
 * @returns {string} - Human readable timezone name
 */
export const getTimezoneDisplayName = (timezone) => {
  try {
    const now = new Date();
    const formatter = new Intl.DateTimeFormat("en-US", {
      timeZone: timezone,
      timeZoneName: "short",
    });

    const parts = formatter.formatToParts(now);
    const timeZoneName = parts.find((part) => part.type === "timeZoneName");
    return timeZoneName ? timeZoneName.value : timezone;
  } catch (error) {
    return timezone;
  }
};

/**
 * Check if timezone conversion is needed
 * @param {string} doctorTimezone
 * @param {string} patientTimezone
 * @returns {boolean}
 */
export const isTimezoneConversionNeeded = (doctorTimezone, patientTimezone) => {
  return (
    doctorTimezone && patientTimezone && doctorTimezone !== patientTimezone
  );
};

/**
 * Test function to verify timezone conversion
 * Example: Doctor in Shanghai (UTC+8) at 09:00 should be 06:00 in Karachi (UTC+5)
 */
export const testTimezoneConversion = () => {
  console.log("Testing timezone conversion...");

  // Test 1: Shanghai to Karachi
  const result1 = convertTimeToPatientTimezone(
    "09:00:00",
    "2025-06-23",
    "Asia/Shanghai",
    "Asia/Karachi",
  );
  console.log("Shanghai 09:00 -> Karachi:", result1);
  // Expected: { time: "06:00:00", date: "2025-06-23", isNextDay: false }

  // Test 2: New York to London
  const result2 = convertTimeToPatientTimezone(
    "14:00:00",
    "2025-06-23",
    "America/New_York",
    "Europe/London",
  );
  console.log("New York 14:00 -> London:", result2);
  // Expected: { time: "19:00:00", date: "2025-06-23", isNextDay: false }

  return { test1: result1, test2: result2 };
};
