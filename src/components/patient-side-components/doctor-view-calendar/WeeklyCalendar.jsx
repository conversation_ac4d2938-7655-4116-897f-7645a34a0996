import React, { useState, useMemo, useEffect } from "react";
import { format, startOfWeek, addDays, parse, addMonths } from "date-fns";
import { getSlotDetailsOfDoctor_api } from "../../../api/api_calls/schedule_apiCalls";
import { toast } from "../../../hooks/use-toast";
import {
  getUserTimezone,
  formatTimeWithTimezone,
  getTimezoneDisplayName,
  isTimezoneConversionNeeded,
} from "../../../helper/timezone-helper";

const WeeklyCalendar = ({
  doctorId,
  minimalMode,
  setSelectedDate,
  selectedDate,
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [slotDetails, setSlotDetails] = useState({ data: [] });
  const [patientTimezone, setPatientTimezone] = useState("");

  // Detect patient's timezone
  useEffect(() => {
    const timezone = getUserTimezone();
    setPatientTimezone(timezone);
  }, []);

  const weekStart = useMemo(
    () => startOfWeek(currentDate, { weekStartsOn: 1 }),
    [currentDate],
  );
  const weekEnd = useMemo(() => addDays(weekStart, 6), [weekStart]);

  const slotMap = useMemo(() => {
    const map = {};
    if (slotDetails && slotDetails.data) {
      slotDetails.data.forEach((item) => {
        map[item.date] = item.slots;
      });
    }
    return map;
  }, [slotDetails]);

  const weekDates = Array.from({ length: 7 }, (_, i) => addDays(weekStart, i));

  const handlePrevWeek = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const newDate = addDays(weekStart, -7);
    if (newDate < today) {
      setCurrentDate(today);
    } else {
      setCurrentDate(addDays(weekStart, -7));
    }
  };

  const handleNextWeek = () => setCurrentDate(addDays(weekStart, 7));

  const handleMonthYearChange = (e) => {
    const [year, month] = e.target.value.split("-");
    const newDate = new Date(Number(year), Number(month) - 1, 1);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (newDate < today) {
      setCurrentDate(today);
    } else {
      setCurrentDate(newDate);
    }
  };

  const getSlotDetails = async (startDate, endDate) => {
    try {
      const res = await getSlotDetailsOfDoctor_api({
        doctorId: doctorId || null,
        startDate,
        endDate,
      });
      setSlotDetails(res);
    } catch (error) {
      toast({
        description: error.message,
        variant: "destructive",
      });
      console.log("ERROR IN getSlotDetails  => ", error);
    }
  };

  // const getDateValue = (slotTime, dateRecieved) => {
  //   const date = new Date(dateRecieved);
  //   const [hours, minutes, seconds] = slotTime.split(":").map(Number);
  //   date.setHours(hours);
  //   date.setMinutes(minutes);
  //   date.setSeconds(seconds);
  //   return date;
  // };

  const getDateValue = (slot, dateRecieved) => {
    const date = new Date(dateRecieved);
    const [hours, minutes, seconds] = slot.startTime.split(":").map(Number);
    date.setHours(hours);
    date.setMinutes(minutes);
    date.setSeconds(seconds);
    // return { ...slot, date };
    return { startTime: slot.startTime, endTime: slot.endTime, date };
  };

  // const handleSlotClick = (slotTime, dateRecieved) => {
  //   setSelectedDate(getDateValue(slotTime.startTime, dateRecieved));
  // };

  // const handleSlotClick = (slot, dateRecieved) => {
  //   if (minimalMode && !slot.isBooked) {
  //     setSelectedDate(getDateValue(slot, dateRecieved));
  //   }
  // };

  const handleSlotClick = (slot, dateRecieved) => {
    if (minimalMode && !slot.isBooked) {
      const newSlot = getDateValue(slot, dateRecieved);
      // Check if the clicked slot is already selected
      if (
        selectedDate &&
        selectedDate.date &&
        new Date(selectedDate.date).getTime() === newSlot.date.getTime() &&
        selectedDate.startTime === slot.startTime
      ) {
        setSelectedDate(null); // Unselect if the same slot is clicked
      } else {
        setSelectedDate(newSlot); // Select the new slot
      }
    }
  };

  useEffect(() => {
    const formattedStart = format(weekStart, "yyyy-MM-dd");
    const formattedEnd = format(weekEnd, "yyyy-MM-dd");
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    if (weekStart < today) {
      const formattedToday = format(today, "yyy-MM-dd");
      getSlotDetails(formattedToday, formattedEnd);
    } else {
      getSlotDetails(formattedStart, formattedEnd);
    }
  }, [currentDate]);

  // Check if any slots have timezone information
  const hasTimezoneInfo = useMemo(() => {
    if (!slotDetails?.data) return false;
    return slotDetails.data.some((dayData) =>
      dayData.slots?.some((slot) => slot.doctorTimezone),
    );
  }, [slotDetails]);

  return (
    <div className="p-4">
      {/* Timezone Information */}
      {hasTimezoneInfo && patientTimezone && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center gap-2">
            <span className="text-blue-600">🌍</span>
            <p className="text-sm text-blue-800">
              <strong>Times shown are converted to your local timezone</strong>{" "}
              ({getTimezoneDisplayName(patientTimezone)}). Original times are in
              doctor's timezone.
            </p>
          </div>
        </div>
      )}

      <div className="flex items-center justify-between mb-4">
        {!minimalMode && (
          <div className="flex items-center space-x-2">
            <select
              value={format(currentDate, "yyyy-MM")}
              onChange={handleMonthYearChange}
              className="border rounded p-2 text-2xl font-bold"
            >
              {Array.from({ length: 6 }, (_, i) => {
                const optionDate = addMonths(new Date(), i);
                return (
                  <option
                    key={format(optionDate, "yyyy-MM")}
                    value={format(optionDate, "yyyy-MM")}
                  >
                    {format(optionDate, "MMMM yyyy")}
                  </option>
                );
              })}
            </select>
          </div>
        )}
        <div
          className={`flex flex-row  ${minimalMode ? "flex-1  justify-between" : "items-center justify-center  gap-2 "}`}
        >
          <button
            onClick={handlePrevWeek}
            className="text-gray-700 hover:text-black py-2 px-4 border-2 rounded-lg"
          >
            &larr;
          </button>
          <button
            onClick={handleNextWeek}
            className="text-gray-700 hover:text-black py-2 px-4 border-2 rounded-lg"
          >
            &rarr;
          </button>
        </div>
      </div>

      <div className="grid grid-cols-7 min-h-[600px]">
        {weekDates.map((date) => {
          const dateStr = format(date, "yyyy-MM-dd");
          const dayName = format(date, "EEE");
          const dayNum = format(date, "dd");
          const monthName = format(date, "MMM");
          const slots = slotMap[dateStr] || [];

          return (
            <div key={dateStr} className="rounded p-2 flex flex-col">
              <div
                className={`${dateStr === format(new Date(), "yyyy-MM-dd") ? "bg-[#0052FD] text-[#fff]" : "bg-white"} p-1 ${minimalMode && "flex justify-center flex-col items-center"}`}
              >
                <div
                  className={`${dateStr === format(new Date(), "yyyy-MM-dd") ? "bg-[#0052FD] text-[#fff]" : "bg-white"}`}
                >
                  {dayName}
                </div>
                {minimalMode ? (
                  <div className="flex flex-row gap-2">
                    <div className="font-semibold">{monthName}</div>
                    <div className="font-semibold">{dayNum}</div>
                  </div>
                ) : (
                  <div className="font-semibold">{dayNum}</div>
                )}
              </div>
              {/* SLOTS */}
              {/* <div className="mt-2 w-full flex flex-1">
                {slots.length > 0 ? (
                  <div className="space-y-1 flex-1">
                    {slots
                      .slice()
                      .sort((a, b) => {
                        const parsedA = parse(
                          a.startTime,
                          "HH:mm:ss",
                          new Date(),
                        );
                        const parsedB = parse(
                          b.startTime,
                          "HH:mm:ss",
                          new Date(),
                        );
                        return parsedA - parsedB;
                      })
                      .map((slotTime) => {
                        const parsedTime = parse(
                          slotTime.startTime,
                          "HH:mm:ss",
                          new Date(),
                        );
                        return (
                          <div
                            key={slotTime.startTime}
                            className={`bg-white border-[1px] border-[#000000] text-black rounded px-1 py-2 text-center ${minimalMode && "cursor-pointer"} ${minimalMode && new Date(selectedDate).getTime() === getDateValue(slotTime.startTime, date).getTime() ? "bg-sky-300" : ""}`}
                            onClick={() => {
                              if (minimalMode) {
                                // handleSlotClick(slotTime, date);
                                console.log("SLOT TIME => ", slotTime);
                              }
                            }}
                          >
                            {format(parsedTime, "h:mm aa")}
                          </div>
                        );
                      })}
                  </div>
                ) : (
                  <div className="relative flex-1 border border-gray-300 rounded-lg overflow-hidden">
                    <div className="absolute inset-0 z-0 bg-[repeating-linear-gradient(45deg,_#e5e5e5_0,_#e5e5e5_5px,_#ffffff_5px,_#ffffff_15px)] pointer-events-none"></div>
                    <div className="relative flex items-center justify-center h-full z-10">
                      <span className="text-gray-700 text-lg font-bold transform -rotate-90">
                        Not Available
                      </span>
                    </div>
                  </div>
                )}
              </div> */}
              <div className="mt-2 w-full flex flex-1">
                {slots.length > 0 ? (
                  <div className="space-y-1 flex-1 text-nowrap">
                    {slots
                      .slice()
                      .sort((a, b) => {
                        const parsedA = parse(
                          a.startTime,
                          "HH:mm:ss",
                          new Date(),
                        );
                        const parsedB = parse(
                          b.startTime,
                          "HH:mm:ss",
                          new Date(),
                        );
                        return parsedA - parsedB;
                      })
                      .map((slot) => {
                        // Convert times if timezone information is available
                        let displayStartTime = slot.startTime;
                        let displayEndTime = slot.endTime;
                        let displayDate = dateStr;

                        if (
                          slot.doctorTimezone &&
                          patientTimezone &&
                          isTimezoneConversionNeeded(
                            slot.doctorTimezone,
                            patientTimezone,
                          )
                        ) {
                          const convertedStart = formatTimeWithTimezone(
                            slot.startTime,
                            dateStr,
                            slot.doctorTimezone,
                            patientTimezone,
                          );
                          const convertedEnd = formatTimeWithTimezone(
                            slot.endTime,
                            dateStr,
                            slot.doctorTimezone,
                            patientTimezone,
                          );

                          displayStartTime = convertedStart.converted.time;
                          displayEndTime = convertedEnd.converted.time;
                          displayDate = convertedStart.converted.date;
                        }

                        const parsedStartTime = parse(
                          displayStartTime,
                          "HH:mm:ss",
                          new Date(),
                        );
                        const parsedEndTime = parse(
                          displayEndTime,
                          "HH:mm:ss",
                          new Date(),
                        );

                        const isSelected =
                          minimalMode &&
                          selectedDate &&
                          selectedDate.date &&
                          new Date(selectedDate.date).getTime() ===
                            getDateValue(slot, date).date.getTime() &&
                          selectedDate.startTime === slot.startTime;

                        return (
                          <div
                            key={slot.startTime}
                            className={`border-[1px] border-[#000000] rounded px-1 py-2 text-center
                              ${minimalMode && !slot.isBooked ? "cursor-pointer" : "cursor-not-allowed"}
                              ${isSelected ? "bg-[#0052FD] text-white" : slot.isBooked ? "bg-gray-300 text-gray-600" : "bg-white text-black"}`}
                            onClick={() => handleSlotClick(slot, date)}
                          >
                            {format(parsedStartTime, "h:mm aa")} -{" "}
                            {format(parsedEndTime, "h:mm aa")}
                            {/* Show timezone conversion indicator */}
                            {slot.doctorTimezone &&
                              patientTimezone &&
                              isTimezoneConversionNeeded(
                                slot.doctorTimezone,
                                patientTimezone,
                              ) && (
                                <div className="text-xs text-gray-500 mt-1">
                                  {displayDate !== dateStr && "(Next day)"}
                                </div>
                              )}
                          </div>
                        );
                      })}
                  </div>
                ) : (
                  <div className="relative flex-1 border border-gray-300 rounded-lg overflow-hidden">
                    <div className="absolute inset-0 z-0 bg-[repeating-linear-gradient(45deg,_#e5e5e5_0,_#e5e5e5_5px,_#ffffff_5px,_#ffffff_15px)] pointer-events-none"></div>
                    <div className="relative flex items-center justify-center h-full z-10">
                      <span className="text-gray-700 text-lg font-bold transform -rotate-90">
                        Not Available
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default WeeklyCalendar;
